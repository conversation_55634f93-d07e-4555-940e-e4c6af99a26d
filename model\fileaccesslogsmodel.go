package model

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ FileAccessLogsModel = (*customFileAccessLogsModel)(nil)

type (
	// FileAccessLogsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFileAccessLogsModel.
	FileAccessLogsModel interface {
		fileAccessLogsModel
		withSession(session sqlx.Session) FileAccessLogsModel
		FindRecentByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*FileAccessLogs, error)
		CountRecentByUserId(ctx context.Context, userId int64) (int64, error)
	}

	customFileAccessLogsModel struct {
		*defaultFileAccessLogsModel
	}
)

// NewFileAccessLogsModel returns a model for the database table.
func NewFileAccessLogsModel(conn sqlx.SqlConn) FileAccessLogsModel {
	return &customFileAccessLogsModel{
		defaultFileAccessLogsModel: newFileAccessLogsModel(conn),
	}
}

func (m *customFileAccessLogsModel) withSession(session sqlx.Session) FileAccessLogsModel {
	return NewFileAccessLogsModel(sqlx.NewSqlConnFromSession(session))
}

// FindRecentByUserId 查询用户最近访问的文件（去重）
func (m *customFileAccessLogsModel) FindRecentByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*FileAccessLogs, error) {
	offset := (page - 1) * pageSize
	// 使用子查询获取每个文件的最新访问记录
	query := fmt.Sprintf(`
		SELECT %s FROM %s
		WHERE id IN (
			SELECT MAX(id) FROM %s
			WHERE user_id = $1
			GROUP BY file_id
		)
		ORDER BY access_time DESC
		LIMIT $2 OFFSET $3
	`, fileAccessLogsRows, m.table, m.table)

	var resp []*FileAccessLogs
	err := m.conn.QueryRowsCtx(ctx, &resp, query, userId, pageSize, offset)
	return resp, err
}

// CountRecentByUserId 统计用户最近访问的不同文件数量
func (m *customFileAccessLogsModel) CountRecentByUserId(ctx context.Context, userId int64) (int64, error) {
	query := fmt.Sprintf("SELECT COUNT(DISTINCT file_id) FROM %s WHERE user_id = $1", m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId)
	return count, err
}
