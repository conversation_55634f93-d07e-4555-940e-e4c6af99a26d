package disk

import (
	"context"
	"errors"
	"fmt"

	"paper-editor-api/internal/middleware"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
	"paper-editor-api/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetRecentFilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取最近访问文件列表
func NewGetRecentFilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRecentFilesLogic {
	return &GetRecentFilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 获取最近访问文件列表
func (l *GetRecentFilesLogic) GetRecentFiles(req *types.FileListReq) (resp *types.FileListResp, err error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询用户最近访问的文件日志
	accessLogs, err := l.svcCtx.FileAccessLogsModel.FindRecentByUserId(l.ctx, userId, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("查询最近访问文件失败: %w", err)
	}

	// 统计总数
	total, err := l.svcCtx.FileAccessLogsModel.CountRecentByUserId(l.ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("统计最近访问文件数量失败: %w", err)
	}

	// 获取文件详细信息
	var fileInfos []types.FileInfo
	for _, accessLog := range accessLogs {
		file, err := l.svcCtx.FilesModel.FindOneByFileId(l.ctx, accessLog.FileId)
		if err != nil {
			logx.Errorf("查询文件信息失败: %v", err)
			continue
		}

		// 查询文件是否被收藏
		starred, err := l.svcCtx.StarredFilesModel.FindByFileIdAndUserId(l.ctx, file.FileId, userId)
		var isStarred bool
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			logx.Errorf("查询文件收藏状态失败: %v", err)
		}
		isStarred = starred != nil

		// 查询文件的标签
		tagRelations, err := l.svcCtx.FileTagRelationsModel.FindTagsByFileId(l.ctx, file.FileId)
		if err != nil {
			logx.Errorf("查询文件标签失败: %v", err)
		}
		var tags []string
		for _, relation := range tagRelations {
			tags = append(tags, relation.TagName)
		}

		fileInfo := types.FileInfo{
			FileId:     file.FileId,
			FileName:   file.FileName,
			FileSize:   file.FileSize,
			FileType:   file.FileType,
			UploadTime: file.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: file.UpdateTime.Format("2006-01-02 15:04:05"),
			FolderId:   file.FolderId.String,
			IsStarred:  isStarred,
			Tags:       tags,
			Status:     int(file.Status),
		}
		fileInfos = append(fileInfos, fileInfo)
	}

	return &types.FileListResp{
		Total:       total,
		Files:       fileInfos,
		Folders:     []types.FolderInfo{}, // 最近访问文件列表不包含文件夹
		FileCount:   total,
		FolderCount: 0,
	}, nil
}
