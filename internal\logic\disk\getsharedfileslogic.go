package disk

import (
	"context"
	"errors"
	"fmt"

	"paper-editor-api/internal/middleware"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
	"paper-editor-api/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSharedFilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取共享文件列表
func NewGetSharedFilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSharedFilesLogic {
	return &GetSharedFilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSharedFilesLogic) GetSharedFiles(req *types.FileListReq) (resp *types.FileListResp, err error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询用户共享的文件
	sharedFiles, err := l.svcCtx.FileSharesModel.FindByUserId(l.ctx, userId, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("查询共享文件失败: %w", err)
	}

	// 统计总数
	total, err := l.svcCtx.FileSharesModel.CountByUserId(l.ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("统计共享文件数量失败: %w", err)
	}

	// 获取文件详细信息
	var fileInfos []types.FileInfo
	for _, sharedFile := range sharedFiles {
		file, err := l.svcCtx.FilesModel.FindOneByFileId(l.ctx, sharedFile.FileId)
		if err != nil {
			logx.Errorf("查询文件信息失败: %v", err)
			continue
		}

		// 查询文件是否被收藏
		starred, err := l.svcCtx.StarredFilesModel.FindByFileIdAndUserId(l.ctx, file.FileId, userId)
		var isStarred bool
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			logx.Errorf("查询文件收藏状态失败: %v", err)
		}
		isStarred = starred != nil

		// 查询文件的标签
		tagRelations, err := l.svcCtx.FileTagRelationsModel.FindTagsByFileId(l.ctx, file.FileId)
		if err != nil {
			logx.Errorf("查询文件标签失败: %v", err)
		}
		var tags []string
		for _, relation := range tagRelations {
			tags = append(tags, relation.TagName)
		}

		fileInfo := types.FileInfo{
			FileId:     file.FileId,
			FileName:   file.FileName,
			FileSize:   file.FileSize,
			FileType:   file.FileType,
			UploadTime: file.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: file.UpdateTime.Format("2006-01-02 15:04:05"),
			FolderId:   file.FolderId.String,
			IsStarred:  isStarred,
			Tags:       tags,
			Status:     int(file.Status),
		}
		fileInfos = append(fileInfos, fileInfo)
	}

	return &types.FileListResp{
		Total:       total,
		Files:       fileInfos,
		Folders:     []types.FolderInfo{}, // 共享文件列表不包含文件夹
		FileCount:   total,
		FolderCount: 0,
	}, nil
}
