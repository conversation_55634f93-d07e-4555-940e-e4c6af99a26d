package disk

import (
	"context"
	"database/sql"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MoveFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 移动文件
func NewMoveFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MoveFileLogic {
	return &MoveFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}


// 移动文件
func (l *MoveFileLogic) MoveFile(req *types.MoveFileReq) (resp *types.BaseResp, err error) {
	
	// 1. 检查文件是否存在
	file, err := l.svcCtx.FilesModel.FindOneByFileId(l.ctx, req.FileId)
	if err != nil {
		return nil, err
	}

	// 2. 检查文件夹是否存在
	folder, err := l.svcCtx.FoldersModel.FindOneByFolderId(l.ctx, req.FolderId)
	if err != nil {
		return nil, err
	}

	// 3. 更新文件的文件夹ID
	file.FolderId = folder.FolderId
	err = l.svcCtx.FilesModel.Update(l.ctx, file)
	if err != nil {
		return nil, err
	}

	return
}
