package model

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ FileSharesModel = (*customFileSharesModel)(nil)

type (
	// FileSharesModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFileSharesModel.
	FileSharesModel interface {
		fileSharesModel
		withSession(session sqlx.Session) FileSharesModel
		FindByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*FileShares, error)
		CountByUserId(ctx context.Context, userId int64) (int64, error)
	}

	customFileSharesModel struct {
		*defaultFileSharesModel
	}
)

// NewFileSharesModel returns a model for the database table.
func NewFileSharesModel(conn sqlx.SqlConn) FileSharesModel {
	return &customFileSharesModel{
		defaultFileSharesModel: newFileSharesModel(conn),
	}
}

func (m *customFileSharesModel) withSession(session sqlx.Session) FileSharesModel {
	return NewFileSharesModel(sqlx.NewSqlConnFromSession(session))
}

// FindByUserId 根据用户ID查询共享文件列表
func (m *customFileSharesModel) FindByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*FileShares, error) {
	offset := (page - 1) * pageSize
	query := fmt.Sprintf("select %s from %s where user_id = $1 and status = 1 order by create_time desc limit $2 offset $3", fileSharesRows, m.table)
	var resp []*FileShares
	err := m.conn.QueryRowsCtx(ctx, &resp, query, userId, pageSize, offset)
	return resp, err
}

// CountByUserId 统计用户共享文件数量
func (m *customFileSharesModel) CountByUserId(ctx context.Context, userId int64) (int64, error) {
	query := fmt.Sprintf("select count(*) from %s where user_id = $1 and status = 1", m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId)
	return count, err
}
